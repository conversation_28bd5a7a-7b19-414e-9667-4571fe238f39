#!/bin/bash

# Test script to verify VRRP configuration synchronization
# This script simulates the Redis operations and checks the generated FRR commands

echo "=== VRRP Configuration Synchronization Test ==="

# Function to run redis commands
run_redis_cmd() {
    echo "Running: redis-cli -p 6379 -n 4 $1"
    redis-cli -p 6379 -n 4 "$1" 2>/dev/null || echo "Redis command failed"
}

# Function to check vrrp config
check_vrrp_config() {
    echo "Current VRRP configuration:"
    vtysh -c "show running-config vrrpd" 2>/dev/null || echo "No VRRP configuration found"
    echo ""
}

# Function to check redis data
check_redis_data() {
    echo "Current Redis VRRP data:"
    redis-cli -p 6379 -n 4 HGETALL "VRRP|Ethernet3|1" 2>/dev/null || echo "No Redis data found"
    echo ""
}

# Function to show logs
show_logs() {
    echo "Recent frrcfgd logs (last 10 lines):"
    tail -20 /var/log/syslog 2>/dev/null | grep frrcfgd | tail -10 || echo "No logs found"
    echo ""
}

# Clean up any existing configuration
echo "=== Cleanup Phase ==="
run_redis_cmd 'DEL "VRRP|Ethernet3|1"'
sleep 2

# Test 1: Create VRRP configuration
echo "=== Test 1: Create VRRP Configuration ==="
echo "Step 1: Creating basic VRRP configuration..."
run_redis_cmd 'HSET "VRRP|Ethernet3|1" vrid "1" priority "100" adv_interval "1000" pre_empt "true" vip "***********"'

echo "Waiting for frrcfgd to process..."
sleep 3

echo "Checking initial configuration:"
check_vrrp_config
check_redis_data

# Test 2: Update VRRP configuration
echo "=== Test 2: Update VRRP Configuration ==="
echo "Step 2: Updating VRRP priority and adding version..."
run_redis_cmd 'HSET "VRRP|Ethernet3|1" priority "150" version "3"'

echo "Waiting for frrcfgd to process..."
sleep 2

echo "Checking updated configuration:"
check_vrrp_config
check_redis_data

# Test 3: Delete specific field
echo "=== Test 3: Delete Specific Field ==="
echo "Step 3: Deleting specific field (priority)..."
run_redis_cmd 'HDEL "VRRP|Ethernet3|1" priority'

echo "Waiting for frrcfgd to process..."
sleep 2

echo "Checking configuration after field deletion:"
check_vrrp_config
check_redis_data

# Test 4: Add IPv6 VRRP
echo "=== Test 4: Add IPv6 VRRP ==="
echo "Step 4: Adding IPv6 VRRP configuration..."
run_redis_cmd 'HSET "VRRP6|Ethernet3|2" vrid "2" priority "200" vip "2001:db8::1"'

echo "Waiting for frrcfgd to process..."
sleep 2

echo "Checking IPv6 VRRP configuration:"
check_vrrp_config

# Test 5: Delete entire VRRP instance
echo "=== Test 5: Delete Entire VRRP Instance ==="
echo "Step 5: Deleting entire IPv4 VRRP instance..."
run_redis_cmd 'DEL "VRRP|Ethernet3|1"'

echo "Waiting for frrcfgd to process..."
sleep 3

echo "Checking configuration after IPv4 instance deletion:"
check_vrrp_config

echo "Step 6: Deleting IPv6 VRRP instance..."
run_redis_cmd 'DEL "VRRP6|Ethernet3|2"'

echo "Waiting for frrcfgd to process..."
sleep 3

echo "Checking final configuration:"
check_vrrp_config

echo "=== Test Summary ==="
echo "All tests completed. Check the logs above for any errors."
show_logs

echo "=== Expected Behavior ==="
echo "1. VRRP instances should be created when Redis data is added"
echo "2. VRRP configuration should be updated when Redis data changes"
echo "3. Specific VRRP parameters should be removed when fields are deleted"
echo "4. Entire VRRP instances should be removed when Redis keys are deleted"
echo "5. Both IPv4 and IPv6 VRRP should work correctly"
