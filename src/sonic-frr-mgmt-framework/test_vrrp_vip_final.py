#!/usr/bin/env python3

"""
Test script to verify the final VRRP VIP fix
"""

class CachedDataWithOp:
    OP_NONE = 0
    OP_ADD = 1
    OP_DELETE = 2
    OP_UPDATE = 3

class CommandArgument:
    def __init__(self, daemon, enabled, val=None):
        self.daemon = daemon
        self.enabled = enabled
        self.value = val

    def __format__(self, format):
        if format == 'no-prefix':
            return 'no ' if not self.enabled else ''
        return str(self.value) if self.value is not None else ''

    def to_str(self):
        return str(self.value) if self.value is not None else ''

class MockDaemon:
    pass

def hdl_vrrp_vip(daemon, cmd, op, st_idx, vals, data):
    """Handle VRRP virtual IP configuration - FINAL VERSION"""
    print(f'hdl_vrrp_vip: st_idx={st_idx}, vals={vals}, op={op}')
    
    if len(vals) < st_idx + 1:
        print(f'hdl_vrrp_vip: insufficient parameters, need at least {st_idx + 1} but got {len(vals)}')
        return None

    # Extract VRRP ID and VIP data
    if st_idx >= 1:
        vrrp_id = vals[st_idx - 1]  # VRRP ID is before the VIP data
    else:
        print(f'hdl_vrrp_vip: invalid st_idx {st_idx}, cannot get VRRP ID')
        return None
        
    vip_data = vals[st_idx]  # VIP data

    print(f'hdl_vrrp_vip: vrrp_id={vrrp_id}, vip_data={vip_data}, op={op}')

    # For DELETE operation, we need to send 'no' command
    cmd_enable = op != CachedDataWithOp.OP_DELETE
    cmd_list = []

    if isinstance(vip_data, list):
        for vip in vip_data:
            no_arg = CommandArgument(daemon, cmd_enable)
            vrid_arg = CommandArgument(daemon, cmd_enable, vrrp_id)
            vip_arg = CommandArgument(daemon, cmd_enable, vip)
            result_cmd = cmd.format(vrid_arg, vip_arg, no=no_arg)
            print(f'VRRP vip command: {result_cmd} (op={op})')
            cmd_list.append(result_cmd)
    else:
        no_arg = CommandArgument(daemon, cmd_enable)
        vrid_arg = CommandArgument(daemon, cmd_enable, vrrp_id)
        vip_arg = CommandArgument(daemon, cmd_enable, vip_data)
        result_cmd = cmd.format(vrid_arg, vip_arg, no=no_arg)
        print(f'VRRP vip command: {result_cmd} (op={op})')
        cmd_list.append(result_cmd)

    return cmd_list

def test_real_scenario():
    """Test the real scenario from the logs"""
    print("Testing real scenario from logs...")
    print("=" * 50)
    
    daemon = MockDaemon()
    cmd_template = '{no:no-prefix}vrrp {} ip {}'
    
    # Real scenario: vals = ['Ethernet4', '1', '*******'], st_idx = 2
    print("1. Real scenario - ADD operation:")
    vals = ['Ethernet4', '1', '*******']
    st_idx = 2
    op = CachedDataWithOp.OP_ADD
    
    result = hdl_vrrp_vip(daemon, cmd_template, op, st_idx, vals, None)
    print(f"Result: {result}")
    print(f"Expected: ['vrrp 1 ip *******']")
    print()
    
    # DELETE operation
    print("2. Real scenario - DELETE operation:")
    op = CachedDataWithOp.OP_DELETE
    
    result = hdl_vrrp_vip(daemon, cmd_template, op, st_idx, vals, None)
    print(f"Result: {result}")
    print(f"Expected: ['no vrrp 1 ip *******']")
    print()

def test_edge_cases():
    """Test edge cases"""
    print("Testing edge cases...")
    print("=" * 50)
    
    daemon = MockDaemon()
    cmd_template = '{no:no-prefix}vrrp {} ip {}'
    
    # Test insufficient parameters
    print("1. Insufficient parameters:")
    vals = ['Ethernet4']
    st_idx = 2
    op = CachedDataWithOp.OP_ADD
    
    result = hdl_vrrp_vip(daemon, cmd_template, op, st_idx, vals, None)
    print(f"Result: {result}")
    print(f"Expected: None")
    print()
    
    # Test invalid st_idx
    print("2. Invalid st_idx:")
    vals = ['*******']
    st_idx = 0
    
    result = hdl_vrrp_vip(daemon, cmd_template, op, st_idx, vals, None)
    print(f"Result: {result}")
    print(f"Expected: None")
    print()

def test_multiple_vips():
    """Test multiple VIPs"""
    print("Testing multiple VIPs...")
    print("=" * 50)
    
    daemon = MockDaemon()
    cmd_template = '{no:no-prefix}vrrp {} ip {}'
    
    # Multiple VIPs
    print("1. Multiple VIPs - ADD operation:")
    vals = ['Ethernet4', '1', ['*******', '*******']]
    st_idx = 2
    op = CachedDataWithOp.OP_ADD
    
    result = hdl_vrrp_vip(daemon, cmd_template, op, st_idx, vals, None)
    print(f"Result: {result}")
    print(f"Expected: ['vrrp 1 ip *******', 'vrrp 1 ip *******']")
    print()

def main():
    """Main test function"""
    print("VRRP VIP Final Fix Test")
    print("=" * 60)
    
    test_real_scenario()
    test_edge_cases()
    test_multiple_vips()
    
    print("Test completed!")

if __name__ == '__main__':
    main()
