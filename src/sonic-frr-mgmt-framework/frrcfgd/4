commit 54472a0f751942e1955cb5d8ec0987d391eff1ed
Merge: c33a38168 62f7c79df
Author: <PERSON> <<EMAIL>>
Date:   Mon Jul 28 17:38:42 2025 +0900

    Pull request #1071: SDNOS-2989  [SONiC][L3][FRR] Implement isis interface password in multifd
    
    Merge in SONIC/sonic-buildimage from SDNOS-2989 to dzsi-master
    
    * commit '62f7c79dfe09b363cf925508bed471fb03f9b3ab':
      SDNOS-2989  [SONiC][L3][FRR] Implement isis interface password in multifd

commit 62f7c79dfe09b363cf925508bed471fb03f9b3ab
Author: <PERSON> <<EMAIL>>
Date:   Mon Jul 28 17:26:21 2025 +0900

    SDNOS-2989  [SONiC][L3][FRR] Implement isis interface password in multifd

commit 7269cb506eca4e611b59e37eb2a5e8a0e976e48a
Author: <PERSON>o <<EMAIL>>
Date:   Thu Jul 24 17:33:55 2025 +0900

    SDNOS-2984 [SONiC][ROUTING] Modifying frrcfgd and template for CLI BGP password

commit 8e14c88e60b9b9e87b7652f727d6ce372fab4e6b
Author: harryning <<EMAIL>>
Date:   Thu Jun 26 14:49:01 2025 +0900

    SDNOS-2813 [M4000 SDNOS] frrcfgd parsing error in pim.

commit 18dd9e8b18d4e195d9ed6d4395896cfdca339a25
Merge: 6d28b954a 5bfebdcc7
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Mon Jun 9 18:16:21 2025 +0900

    Pull request #961: SDNOS-2633 [SONiC][Framework][FRR-vtysh/SONic-CLI][Routing][ospfv2/ospfv3] Area interface : authentication [message-digest]
    
    Merge in SONIC/sonic-buildimage from SDNOS-2633 to dzsi-master
    
    * commit '5bfebdcc72e8e420c4158bfca7c14ce74c718489':
      SDNOS-2633 [SONiC][Framework][FRR-vtysh/SONic-CLI][Routing][ospfv2/ospfv3] Area interface : authentication [message-digest]

commit 5bfebdcc72e8e420c4158bfca7c14ce74c718489
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Mon Jun 9 17:52:40 2025 +0900

    SDNOS-2633 [SONiC][Framework][FRR-vtysh/SONic-CLI][Routing][ospfv2/ospfv3] Area interface : authentication [message-digest]

commit d8cefc66c38e1be8cee7a3854b5c40b1285dc8ac
Author: Brian Dung Viet Vo <<EMAIL>>
Date:   Sun May 18 23:41:33 2025 +0900

    SDNOS-2485 [SONiC][Mgmt-framework] Adding frrcfgd handling for CLI redistribute external route to router OSPFv3 in mgmt-framework

commit 477caea77d6785cf8cd64ad169d5fa17dd517740
Author: Brian Dung Viet Vo <<EMAIL>>
Date:   Wed May 14 19:50:57 2025 +0900

    SDNOS-2481 [SONiC][Mgmt-framework] Implement CLI to configure prefix-list in mgmt-framework

commit b718f99214566204ed3ea9ee354ddeec8be20d82
Merge: ce7ba8328 ecbed5cb4
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Mon May 12 15:38:17 2025 +0900

    Pull request #838: SDNOS-2400 Bringup multifd daemon and starting multifd as daemon critical process
    
    Merge in SONIC/sonic-buildimage from SDNOS-2400 to dzsi-master
    
    * commit 'ecbed5cb406a26253d1bf22375b77d0fedda21a2':
      SDNOS-2400 add submodule sonic-frr : bring up multifd
      SDNOS-2400 Bringup multifd daemon and starting multifd as daemon critical process

commit 0610f8fffc5174b0041bbd5b6a5c8c3d6a9edacb
Author: Joseph Mikhael <<EMAIL>>
Date:   Thu Apr 10 03:07:38 2025 +0900

    SDNOS-2097:frrcfgd changes for route map handling

commit f8fb41db48d83bdec7d7957c6735a45499147a77
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Mon Apr 21 13:47:25 2025 +0900

    SDNOS-2400 Bringup multifd daemon and starting multifd as daemon critical process

commit 29d1a1d650b40643c0ec7b8a3cd4bbbc2195b5f5
Author: Brian Dung Viet Vo <<EMAIL>>
Date:   Thu Apr 10 12:58:24 2025 +0900

    SDNOS-2388 Add support vrf name for ospf redistribute CLI

commit 081f8b874338915c4197f12273ca9c0c2ad6bd75
Author: Brian Dung Viet Vo <<EMAIL>>
Date:   Fri Feb 28 18:13:12 2025 +0900

    SDNOS-2247 [SONiC][FRAMEWORK] Changing daemon for supporting static route command in frrcfgd from staticd to mgmtd

commit b78f9e216b89b14dbc4ff4cb63f7befb1147af00
Merge: a8663bc7f 9f3d81e87
Author: Choi Sungho <<EMAIL>>
Date:   Sat Jan 4 07:08:48 2025 +0900

    SDNOS-2011 initial merge of dzsi-development into dzsi-master (202412 base)

commit 2e3e028f2f4eef20441e64e5b39ba58a48588eee
Author: Linsongnan <<EMAIL>>
Date:   Sat Nov 30 02:36:57 2024 +0800

    [sonic-frr-mgmt-framework]: refactor the way of load srv6 locator opcode config (#20954)
    
    [sonic-frr-mgmt-framework]: refactor the way of load srv6 locator opcode config
    
    Signed-off-by: linsongnan <<EMAIL>>

commit 174d7261bf97c60dfb2e7b516e74c53f330b9b53
Author: Yubin-Li <<EMAIL>>
Date:   Sat Nov 16 01:27:13 2024 +0800

    [frrcfgd]: add locator func to frrcfgd (#20330)
    
    Signed-off-by: ysj17861081006 <<EMAIL>>

commit 08a156526dd3be425e7dc77696b8173562780f93
Author: Eddie Ruan <<EMAIL>>
Date:   Fri Sep 6 22:39:04 2024 +0800

    Add VPN related configuration in frrcfgd.py (#19924)
    
    Add VPN related configuration in frrcfgd.py

commit fc4eb97938265272defb3371febba57c7d824c22
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Mon Jun 3 19:29:34 2024 +0900

    SDNOS-1689 [isis] implement the new concept of interface address-family ipv4/ipv6 unicast/multicast and re-apply dependencies

commit 6af28874cad2c0ceb9709901640918c123a5ca83
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Mon Jun 3 16:12:56 2024 +0900

    SDNOS-1688 [isis] implement interfaces commands for CSNP/PSNP interval, Hello interval/multiplier, metric, priority

commit ced10e02474614cfc48e950aaa9dc8e2d66e749d
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Thu May 30 16:56:50 2024 +0900

    SDNOS-1684 SDNOS-1685 SDNOS-1686 [isis] implement mgmt-framework commands for metric-style, spf-interval, spf prefix-priority

commit 98e81ace4df395a98ff7797064096ec1647b1146
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Wed May 29 17:27:51 2024 +0900

    SDNOS-1678 SDNOS-1680 SDNOS-1681 [isis] implement lsp-gen-interval, lsp-refresh-interval, max-lsp-lifetime for global mode - remove all ISIS_LEVEL table

commit 25ba4f1b79848460ffed58b19c6ed603ab09aaeb
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Wed May 29 14:09:28 2024 +0900

    SDNOS-1678 SDNOS-1680 SDNOS-1681 [isis] implement lsp-gen-interval, lsp-refresh-interval, max-lsp-lifetime for global mode

commit 227fde0bb1c926fc6292cea9976a965a1a19f884
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Mon May 27 19:34:01 2024 +0900

    SDNOS-1679 [isis] support lsp-mtu command in global

commit 6061fbe40a9babc4a0d348098f640c670532e3eb
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Mon May 27 13:01:25 2024 +0900

    SDNOS-1677 [isis] support hostname dynamic command

commit e19c9f0ce4cb000a41c46008d4474ea4b7599bf1
Author: duy.phamvan <<EMAIL>>
Date:   Wed May 15 18:32:49 2024 +0900

    SDNOS-1671 (ospf) support translate OSPF authentication CLI

commit 9d673205020821e4c0ac3f2b4d451a99a1cc0166
Author: duy.phamvan <<EMAIL>>
Date:   Wed Apr 24 18:09:34 2024 +0900

    SDNOS-1644 (ospf) implement frrcfgd to translate OSPF priority, transmit-interval CLI

commit cf222ee4720dec9c3aed201574ae84b4618e8dee
Author: duy.phamvan <<EMAIL>>
Date:   Tue Apr 23 17:33:58 2024 +0900

    SDNOS-1642 (ospf) translate OSPF CLI to config hello-interval & dead-interval

commit b298f5d2633e195e7f472f71a026442fadca94ea
Author: duy.phamvan <<EMAIL>>
Date:   Mon Apr 22 16:51:32 2024 +0900

    SDNOS-1631 (ospf) translate CLI config cost for OSPF

commit 6b1ce138f14402a596f1c93c588f5a6199d97821
Author: duy.phamvan <<EMAIL>>
Date:   Tue Apr 16 13:17:03 2024 +0900

    SDNOS-1625 (ospf) add network_type in network command

commit cf34d42246ff64f17a2ea5472c471d8b20e9fbec
Merge: 0b92b223c 70fbc99a6
Author: Nick Duy Van Pham <<EMAIL>>
Date:   Wed Apr 3 00:25:51 2024 +0900

    Pull request #556: SDNOS-1536 Support frrcfgd for new format of OSPF interface table
    
    Merge in SONIC/sonic-buildimage from SDNOS-1536 to dzsi-development
    
    * commit '70fbc99a6a7efa2e61737ab1faece98da6e0b0bd':
      SDNOS-1536 Modify table key in OSPFv2 & OSPFv3 interface mode

commit 70fbc99a6a7efa2e61737ab1faece98da6e0b0bd
Author: duy.phamvan <<EMAIL>>
Date:   Tue Apr 2 19:17:12 2024 +0900

    SDNOS-1536 Modify table key in OSPFv2 & OSPFv3 interface mode

commit db7e9e6fb8a0e13e059cd68b0ec10e1f14dbe042
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Tue Apr 2 18:26:52 2024 +0900

    SDNOS-1588 [isis] support interface ip/ipv6 router

commit 53850ead941a9082a5aa11c18e1e662bb1d5a182
Author: duy.phamvan <<EMAIL>>
Date:   Sat Mar 30 11:12:21 2024 +0900

    SDNOS-1585 (ospfv3) support translating CLI for OSPFv3 global & interface mode

commit 693ae0cf612c716b5899602d4027b983dac4bede
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Fri Mar 29 01:08:42 2024 +0900

    SDNOS-1582 add submodules - [isis] add leaf level-circuit-type for router isis interface

commit 5bf8a7284a2025597eaad88a91773ca034406238
Merge: 1feee36c0 e62ced01f
Author: duy.phamvan <<EMAIL>>
Date:   Thu Mar 28 18:35:37 2024 +0900

    Merge branch 'dzsi-development' into SDNOS-1536

commit b20d71803064c41951d1c64841330ed44f3de03e
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Thu Mar 28 18:29:37 2024 +0900

    SDNOS-1581 [frrcfgd][isis] implement conversion to FRR commands for ISIS_INTERFACE_AF table in REDIS CONFIG DB

commit 6d2eed9582da5385252c43e74aa1a430487b78d1
Merge: f7949df80 9be7f6181
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Thu Mar 28 17:23:34 2024 +0900

    Pull request #540: SDNOS-1579 [frrcfgd][isis] implement conversion to FRR commands for ISIS_INTERFACE table in REDIS CONFIG DB
    
    Merge in SONIC/sonic-buildimage from SDNOS-1579 to dzsi-development
    
    * commit '9be7f61819cf78fc8ef33dd8f39936ceb4deea4c':
      SDNOS-1579 [frrcfgd][isis] implement conversion to FRR commands for ISIS_INTERFACE table in REDIS CONFIG DB

commit 9be7f61819cf78fc8ef33dd8f39936ceb4deea4c
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Thu Mar 28 17:18:03 2024 +0900

    SDNOS-1579 [frrcfgd][isis] implement conversion to FRR commands for ISIS_INTERFACE table in REDIS CONFIG DB

commit f4b57aeacc7e2176165670ddec0e0ccdb26dc064
Author: Brian Dung Viet Vo <<EMAIL>>
Date:   Thu Mar 28 16:07:19 2024 +0900

    SDNOS-1580 Implement frrcfgd for CLI [no] bgp default ipv4-unicast

commit 1feee36c0940299d44d58d8d9d3d4e4943067ec1
Author: duy.phamvan <<EMAIL>>
Date:   Thu Mar 28 14:02:30 2024 +0900

    SDNOS-1536 (ospf) translate CLI 'passive' in frrcfgd

commit 4bef2091e8cdc071b01f32e3c4342c35031cb20a
Merge: 32060f8e0 692f0a2f0
Author: duy.phamvan <<EMAIL>>
Date:   Thu Mar 28 13:19:28 2024 +0900

    Merge branch 'dzsi-development' into SDNOS-1536

commit 32060f8e0a2df85623faced93102545fe64878a1
Author: duy.phamvan <<EMAIL>>
Date:   Thu Mar 28 13:14:19 2024 +0900

    SDNOS-1536 (ospf) modify frrcfgd for translating OSPF CLI

commit 4812c47fd0df749897885bd9fc26f27486ab70df
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Thu Mar 28 12:12:22 2024 +0900

    SDNOS-1578 [frrcfgd][isis] implement conversion to FRR commands for ISIS_GLOBAL_AF table in REDIS CONFIG DB

commit 8733202ccab8e1daf44c2ec537a01ef1e90a3bd8
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Thu Mar 28 11:27:56 2024 +0900

    SDNOS-1576 [frrcfgd][isis] implement conversion to FRR commands for ISIS_GLOBAL_NET table in REDIS CONFIG DB

commit 4c5b96a955addb4ebf360c7df66ad5a65729e4a3
Author: Bernard Dung Huu Nguyen <<EMAIL>>
Date:   Wed Mar 27 17:25:59 2024 +0900

    SDNOS-1573 [frrcfgd][isis] implement conversion to FRR commands for ISIS_GLOBAL table in REDIS CONFIG DB

commit a73d443c1d02698d9f3e030947c469677798bd32
Author: Guilt <<EMAIL>>
Date:   Wed May 24 19:01:43 2023 +0200

    [CI][doc][build] Trim src folder files trailing blanks (#15162)
    
    - Run pre-commit tox profile to trim all trailing blanks
    - Use several commits with a per-folder based strategy
      to ease their merge
    
    Issue #15114
    
    Signed-off-by: Guillaume Lambert <<EMAIL>>

commit fabb30f2e98db967064daed757aeb221bee0c323
Author: Hua Liu <<EMAIL>>
Date:   Tue Feb 28 18:01:49 2023 -0800

    Fix swsscommon psubscribe code break in frrcfgd (#13836)
    
    Fix swsscommon psubscribe code break in frrcfgd
    
    #### Why I did it
    Fix frrcfgd psubscribe code break: https://github.com/sonic-net/sonic-buildimage/issues/13109
    The code issue caused by API change when migrate from swsssdk to swsscommon
    
    #### How I did it
    Fix frrcfgd code to use swsscommon psubscribe API.
    
    #### How to verify it
    Pass all UT.
    Manually check fixed code work correctly.

commit 6713390d884518bb79bb4b44003e67d94a35684d
Author: Antonio GyeRok GWUN 권계록 <<EMAIL>>
Date:   Fri Jan 27 15:15:57 2023 +0900

    SDNOS-448 handle frrcfgd issue

commit a9b7a1facd0c004b4a230ecac915a85d664ebb3c
Author: Hua Liu <<EMAIL>>
Date:   Mon Jul 11 10:01:10 2022 +0800

    Replace swsssdk with swsscommon (#11215)
    
    #### Why I did it
    Update scripts in sonic-buildimage from py-swsssdk to swsscommon
    
    
    #### How I did it
    Change code to use swsscommon.
    
    #### How to verify it
    Pass all E2E test case
    
    #### Which release branch to backport (provide reason below if selected)
    
    <!--
    - Note we only backport fixes to a release branch, *not* features!
    - Please also provide a reason for the backporting below.
    - e.g.
    - [x] 202006
    -->
    
    - [ ] 201811
    - [ ] 201911
    - [ ] 202006
    - [ ] 202012
    - [ ] 202106
    - [ ] 202111
    - [ ] 202205
    
    #### Description for the changelog
    Update scripts in sonic-buildimage from py-swsssdk to swsscommon
    
    #### Link to config_db schema for YANG module changes
    <!--
    Provide a link to config_db schema for the table for which YANG model
    is defined
    Link should point to correct section on https://github.com/Azure/sonic-buildimage/blob/master/src/sonic-yang-models/doc/Configuration.md
    -->
    
    #### A picture of a cute animal (not mandatory but encouraged)

commit e8adee2c83092de56d28d6a73a0646edb1049450
Author: Dmytro <<EMAIL>>
Date:   Wed Oct 13 04:54:37 2021 +0300

    [frrcfgd][bgpcfgd] Add portchannel support (#8911)
    
    * To add portchannel support in frrcfgd and bgpcfgd
    * Update is_zero_ip() to handle portchannel name
    Signed-off-by: d-dashkov <<EMAIL>>

commit a171e6c5e4e51a360a70190903e09decb44ec7e3
Author: Zhenhong Zhao <<EMAIL>>
Date:   Sun Jan 24 17:57:03 2021 -0800

    [frrcfgd] introduce frrcfgd to manage frr config when frr_mgmt_framework_config is true (#5142)
    
    - Support for non-template based FRR configurations (BGP, route-map, OSPF, static route..etc) using config DB schema.
    - Support for save & restore - Jinja template based config-DB data read and apply to FRR during startup
    
    **- How I did it**
    
    - add frrcfgd service
    - when frr_mgmg_framework_config is set, frrcfgd starts in bgp container
    - when user changed the BGP or other related table entries in config DB, frrcfgd will run corresponding VTYSH commands to program on FRR.
    - add jinja template to generate FRR config file to be used by FRR daemons while bgp container restarted
    
    **- How to verify it**
    1. Add/delete data on config DB and then run VTYSH "show running-config" command to check if FRR configuration changed.
    1. Restart bgp container and check if generated FRR config file is correct and run VTYSH "show running-config" command to check if FRR configuration is consistent with attributes in config DB
    
    Co-authored-by: Zhenhong Zhao <<EMAIL>>
