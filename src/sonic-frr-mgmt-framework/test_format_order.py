#!/usr/bin/env python3

"""
Test script to verify Python format method parameter order
"""

class CommandArgument:
    def __init__(self, daemon, enabled, val=None):
        self.daemon = daemon
        self.enabled = enabled
        self.value = val

    def __format__(self, format):
        if format == 'no-prefix':
            return 'no ' if not self.enabled else ''
        return str(self.value) if self.value is not None else ''

    def to_str(self):
        return str(self.value) if self.value is not None else ''

class MockDaemon:
    pass

def test_format_order():
    """Test the format method parameter order"""
    print("Testing Python format method parameter order...")
    print("=" * 50)
    
    daemon = MockDaemon()
    
    # Test template: '{no:no-prefix}vrrp {} ip {}'
    template = '{no:no-prefix}vrrp {} ip {}'
    
    # Test ADD operation (cmd_enable = True)
    print("1. ADD Operation (cmd_enable = True):")
    no_arg = CommandArgument(daemon, True)  # Should return empty string for no-prefix
    vrid_arg = CommandArgument(daemon, True, '1')  # VRRP ID
    vip_arg = CommandArgument(daemon, True, '*******')  # VIP address
    
    result = template.format(vrid_arg, vip_arg, no=no_arg)
    print(f"Template: {template}")
    print(f"Parameters: vrid_arg='1', vip_arg='*******', no=no_arg(enabled=True)")
    print(f"Result: '{result}'")
    print(f"Expected: 'vrrp 1 ip *******'")
    print(f"Correct: {result == 'vrrp 1 ip *******'}")
    print()
    
    # Test DELETE operation (cmd_enable = False)
    print("2. DELETE Operation (cmd_enable = False):")
    no_arg = CommandArgument(daemon, False)  # Should return 'no ' for no-prefix
    vrid_arg = CommandArgument(daemon, False, '1')  # VRRP ID
    vip_arg = CommandArgument(daemon, False, '*******')  # VIP address
    
    result = template.format(vrid_arg, vip_arg, no=no_arg)
    print(f"Template: {template}")
    print(f"Parameters: vrid_arg='1', vip_arg='*******', no=no_arg(enabled=False)")
    print(f"Result: '{result}'")
    print(f"Expected: 'no vrrp 1 ip *******'")
    print(f"Correct: {result == 'no vrrp 1 ip *******'}")
    print()

def test_wrong_order():
    """Test what happens with wrong parameter order"""
    print("3. Testing WRONG parameter order (for comparison):")
    
    daemon = MockDaemon()
    template = '{no:no-prefix}vrrp {} ip {}'
    
    no_arg = CommandArgument(daemon, True)
    vrid_arg = CommandArgument(daemon, True, '1')
    vip_arg = CommandArgument(daemon, True, '*******')
    
    # Wrong order: putting no_arg as first positional parameter
    try:
        result = template.format(no_arg, vrid_arg, vip_arg)
        print(f"Wrong order result: '{result}'")
        print("This would be wrong because no_arg goes to first {}, not {no:no-prefix}")
    except Exception as e:
        print(f"Error with wrong order: {e}")
    print()

def test_simple_format():
    """Test simple format to understand the mechanism"""
    print("4. Simple format test to understand mechanism:")
    
    # Simple test
    template = "Hello {name}, you have {} messages and {} notifications"
    result = template.format("5", "3", name="Alice")
    print(f"Template: {template}")
    print(f"Call: template.format('5', '3', name='Alice')")
    print(f"Result: {result}")
    print("Explanation:")
    print("  - '5' goes to first {} (messages)")
    print("  - '3' goes to second {} (notifications)")  
    print("  - name='Alice' goes to {name}")
    print()

def main():
    """Main test function"""
    print("Python Format Method Parameter Order Test")
    print("=" * 60)
    
    test_format_order()
    test_wrong_order()
    test_simple_format()
    
    print("Conclusion:")
    print("- Named parameters like {no:no-prefix} are filled by keyword arguments")
    print("- Positional parameters like {} are filled by positional arguments in order")
    print("- Our fix is CORRECT: format(vrid_arg, vip_arg, no=no_arg)")

if __name__ == '__main__':
    main()
