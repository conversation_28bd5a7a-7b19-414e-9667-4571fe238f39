#!/usr/bin/env python3

"""
Test script to verify VRRP VIP command formatting fix
"""

class CachedDataWithOp:
    OP_NONE = 0
    OP_ADD = 1
    OP_DELETE = 2
    OP_UPDATE = 3

class CommandArgument:
    def __init__(self, daemon, enabled, val=None):
        self.daemon = daemon
        self.enabled = enabled
        self.value = val

    def __format__(self, format):
        if format == 'no-prefix':
            return 'no ' if not self.enabled else ''
        return str(self.value) if self.value is not None else ''

    def to_str(self):
        return str(self.value) if self.value is not None else ''

class MockDaemon:
    pass

def get_vrrp_id_from_data(vrrp_instance_id, data):
    """Get the actual VRRP ID to use in commands"""
    if data and 'vrid' in data:
        return data['vrid'].data
    return vrrp_instance_id

def hdl_vrrp_vip(daemon, cmd, op, st_idx, vals, data):
    """Handle VRRP virtual IP configuration - FIXED VERSION"""
    if len(vals) < st_idx + 2:
        return None

    vrrp_instance_id = vals[st_idx]  # This is the VRRP ID from the key
    vip_data = vals[st_idx + 1]

    # Use the vrrp_instance_id directly as the VRRP ID
    vrid_value = vrrp_instance_id
    print(f'hdl_vrrp_vip: vrid_value={vrid_value}, vip_data={vip_data}, op={op}')

    # For DELETE operation, we need to send 'no' command
    cmd_enable = op != CachedDataWithOp.OP_DELETE
    cmd_list = []

    if isinstance(vip_data, list):
        for vip in vip_data:
            no_arg = CommandArgument(daemon, cmd_enable)
            vrid_arg = CommandArgument(daemon, cmd_enable, vrid_value)
            vip_arg = CommandArgument(daemon, cmd_enable, vip)
            # Command template: '{no:no-prefix}vrrp {} ip {}' - use named 'no' parameter
            result_cmd = cmd.format(vrid_arg, vip_arg, no=no_arg)
            print(f'VRRP vip command: {result_cmd} (op={op})')
            cmd_list.append(result_cmd)
    else:
        no_arg = CommandArgument(daemon, cmd_enable)
        vrid_arg = CommandArgument(daemon, cmd_enable, vrid_value)
        vip_arg = CommandArgument(daemon, cmd_enable, vip_data)
        # Command template: '{no:no-prefix}vrrp {} ip {}' - use named 'no' parameter
        result_cmd = cmd.format(vrid_arg, vip_arg, no=no_arg)
        print(f'VRRP vip command: {result_cmd} (op={op})')
        cmd_list.append(result_cmd)

    return cmd_list

def test_vrrp_vip_add():
    """Test VRRP VIP ADD operation"""
    print("Testing VRRP VIP ADD operation...")

    daemon = MockDaemon()
    cmd_template = '{no:no-prefix}vrrp {} ip {}'
    op = CachedDataWithOp.OP_ADD
    st_idx = 0
    vals = ['1', '*******']  # vrrp_instance_id, vip_data
    data = None  # In real scenario, this is None from key mapping

    result = hdl_vrrp_vip(daemon, cmd_template, op, st_idx, vals, data)
    print(f"Result: {result}")
    print(f"Expected: ['vrrp 1 ip *******']")
    print()

def test_vrrp_vip_delete():
    """Test VRRP VIP DELETE operation"""
    print("Testing VRRP VIP DELETE operation...")
    
    daemon = MockDaemon()
    cmd_template = '{no:no-prefix}vrrp {} ip {}'
    op = CachedDataWithOp.OP_DELETE
    st_idx = 0
    vals = ['1', '*******']  # vrrp_instance_id, vip_data
    data = None  # In real scenario, this is None from key mapping
    
    result = hdl_vrrp_vip(daemon, cmd_template, op, st_idx, vals, data)
    print(f"Result: {result}")
    print(f"Expected: ['no vrrp 1 ip *******']")
    print()

def test_vrrp_vip_list():
    """Test VRRP VIP with multiple IPs"""
    print("Testing VRRP VIP with multiple IPs...")
    
    daemon = MockDaemon()
    cmd_template = '{no:no-prefix}vrrp {} ip {}'
    op = CachedDataWithOp.OP_ADD
    st_idx = 0
    vals = ['1', ['*******', '*******']]  # vrrp_instance_id, vip_data_list
    data = {'vrid': type('obj', (object,), {'data': '1'})()}  # Mock data with vrid
    
    result = hdl_vrrp_vip(daemon, cmd_template, op, st_idx, vals, data)
    print(f"Result: {result}")
    print(f"Expected: ['vrrp 1 ip *******', 'vrrp 1 ip *******']")
    print()

def main():
    """Main test function"""
    print("VRRP VIP Command Formatting Fix Test")
    print("=" * 50)
    
    test_vrrp_vip_add()
    test_vrrp_vip_delete()
    test_vrrp_vip_list()
    
    print("Test completed!")

if __name__ == '__main__':
    main()
