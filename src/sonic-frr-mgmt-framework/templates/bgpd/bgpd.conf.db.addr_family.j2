{# -----------------------------------------------------------------------------------------#}
{# this is invoked with the "vrf" variable set						    #}
{# -----------------------------------------------------------------------------------------#}
{# -------address-family --------------------------- #}
{% if (BGP_GLOBALS_AF is defined and BGP_GLOBALS_AF|length > 0) or
      (BGP_GLOBALS_AF_NETWORK is defined and BGP_GLOBALS_AF_NETWORK|length > 0) or
      (BGP_GLOBALS_AF_AGGREGATE_ADDR is defined and BGP_GLOBALS_AF_AGGREGATE_ADDR|length > 0) or
      (ROUTE_REDISTRIBUTE is defined and ROUTE_REDISTRIBUTE|length > 0) or
      (BGP_PEER_GROUP_AF is defined and BGP_PEER_GROUP_AF|length > 0) or
      (BGP_NEIGHBOR_AF is defined and BGP_NEIGHBOR_AF|length > 0) %}
{% set af_str_map = { 'ipv4_unicast': 'ipv4', 'ipv6_unicast':'ipv6', 'l2vpn_evpn':'l2vpn'} %}
{% set safi_str_map = { 'ipv4_unicast': 'unicast', 'ipv6_unicast':'unicast', 'l2vpn_evpn':'evpn'} %}
{% for afi_safi_key, af_ag_val in BGP_GLOBALS_AF.items() %}
{% set af =  afi_safi_key[1] %}
 !
 address-family {{af_str_map[af]}} {{safi_str_map[af]}}
{% if BGP_GLOBALS_AF is defined and BGP_GLOBALS_AF|length > 0 %}
{% for af_key, af_val in BGP_GLOBALS_AF.items() %}
{% if vrf == af_key[0] and af == af_key[1] %}
{% if 'ebgp_route_distance' in af_val and 'ibgp_route_distance' in af_val and 'local_route_distance' in af_val %}
  distance bgp {{af_val['ebgp_route_distance']}} {{af_val['ibgp_route_distance']}} {{af_val['local_route_distance']}}
{% endif %}
{% if 'import_vrf' in af_val %}
  import vrf {{af_val['import_vrf']}}
{% endif %}
{% if 'import_vrf_route_map' in af_val %}
  import vrf route-map {{af_val['import_vrf_route_map']}}
{% endif %}
{% endif %}
{% endfor %}
{% endif %}
{# ------- network-prefix    --------------------------- #}
{% if BGP_GLOBALS_AF_NETWORK is defined and BGP_GLOBALS_AF_NETWORK|length > 0 %}
{% for af_nw_key, af_nw_val in BGP_GLOBALS_AF_NETWORK.items() %}
{% if vrf == af_nw_key[0] and af == af_nw_key[1] %}
{% set af_nw_ns = namespace(nw_end = '') %}
{% if 'backdoor' in af_nw_val and af_nw_val['backdoor'] == 'true' %}
{% set af_nw_ns.nw_end = 'backdoor ' %}
{% endif %}
{% if 'policy' in af_nw_val %}
{% set af_nw_ns.nw_end = af_nw_ns.nw_end + 'route-map ' + af_nw_val['policy'] %}
{% endif %}
  network {{af_nw_key[2]}} {{af_nw_ns.nw_end}}
{% endif %}
{% endfor %}
{% endif %}
{# ------- network-prefix end   --------------------------- #}
{# ------- aggregate-prefix -Start--------------------------- #}
{% if BGP_GLOBALS_AF_AGGREGATE_ADDR is defined and BGP_GLOBALS_AF_AGGREGATE_ADDR|length > 0 %}
{% for af_ag_key, af_ag_val in BGP_GLOBALS_AF_AGGREGATE_ADDR.items() %}
{% set af_ag_ns = namespace(ag_end = '') %}
{% if vrf == af_ag_key[0] and af == af_ag_key[1] %}
{% if 'as_set' in af_ag_val and af_ag_val['as_set'] == 'true' %}
{% set af_ag_ns.ag_end = 'as-set ' %}
{% endif %}
{% if 'summary_only' in af_ag_val and af_ag_val['summary_only'] == 'true' %}
{% set af_ag_ns.ag_end = af_ag_ns.ag_end + 'summary-only' %}
{% endif %}
  aggregate-address {{af_ag_key[2]}} {{af_ag_ns.ag_end}}
{% endif %}
{% endfor %}
{% endif %}
{# ------- aggregate-prefix - End --------------------------- #}
{# ------- redistribute - Start --------------------------- #}
{% if ROUTE_REDISTRIBUTE is defined and ROUTE_REDISTRIBUTE|length > 0 %}
{% for rr_key, rr_val in ROUTE_REDISTRIBUTE.items() %}
{% if vrf == rr_key[0] and af_str == rr_key[3] %}
{% if 'route_map' in rr_val %}
{% for rmap in rr_val['route_map'] %}
  redistribute {{rr_key[1]}} route-map {{rmap}}
{% endfor %}
{% else %}
  redistribute {{rr_key[1]}}
{% endif %}
{% endif %}
{% endfor %}
{% endif %}
{# ------- redistribute - End ----------------------------- #}
{# ------- peer-group af--------------------------- #}
{% if BGP_PEER_GROUP_AF is defined and BGP_PEER_GROUP_AF|length > 0 %}
{% for n_af_key, n_af_val in BGP_PEER_GROUP_AF.items() %}
{% if vrf == n_af_key[0] and af == n_af_key[2] %}
{% set nbr_name = n_af_key[1] %}
{% include "bgpd.conf.db.nbr_af.j2" %}
{% endif %}
{% endfor %}
{% endif %}
{# ------- peer-group af end --------------------------- #}
{# ------- neighbor af--------------------------- #}
{% if BGP_NEIGHBOR_AF is defined and BGP_NEIGHBOR_AF|length > 0 %}
{% for n_af_key, n_af_val in BGP_NEIGHBOR_AF.items() %}
{% if vrf == n_af_key[0] and af == n_af_key[2] %}
{% set nbr_name = n_af_key[1] %}
{% include "bgpd.conf.db.nbr_af.j2" %}
{% endif %}
{% endfor %}
{% endif %}
{# ------- neighbor af end --------------------------- #}
{% if BGP_GLOBALS_AF is defined and BGP_GLOBALS_AF|length > 0 %}
{% for af_key, af_val in BGP_GLOBALS_AF.items() %}
{% if vrf == af_key[0] and af == af_key[1] %}
{# -------bgp dampen - start--------------------------- #}
{% if 'route_flap_dampen' in af_val and af_val['route_flap_dampen'] == 'true' %}
{% set ns = namespace(route_dampen = '') %}
{% if 'route_flap_dampen_half_life' in af_val  %}
{% set ns.route_dampen = ns.route_dampen + af_val['route_flap_dampen_half_life'] + ' ' %}
{% if 'route_flap_dampen_reuse_threshold' in af_val  %}
{% set ns.route_dampen = ns.route_dampen + af_val['route_flap_dampen_reuse_threshold'] + ' ' %}
{% if 'route_flap_dampen_suppress_threshold' in af_val  %}
{% set ns.route_dampen = ns.route_dampen + af_val['route_flap_dampen_suppress_threshold'] + ' ' %}
{% if 'route_flap_dampen_max_suppress' in af_val  %}
{% set ns.route_dampen = ns.route_dampen + af_val['route_flap_dampen_max_suppress'] + ' ' %}
{% endif %}
{% endif %}
{% endif %}
{% endif %}
  bgp dampening {{ns.route_dampen}}
{% endif %}
{# -------bgp dampen - end --------------------------- #}
{% if 'max_ebgp_paths' in af_val %}
  maximum-paths {{af_val['max_ebgp_paths']}}
{% endif %}
{% if 'max_ibgp_paths' in af_val %}
{% set ns = namespace(max_ibgp = af_val['max_ibgp_paths']) %}
{% if 'ibgp_equal_cluster_length' in af_val and af_val['ibgp_equal_cluster_length'] == 'true' %}
{% set ns.max_ibgp = ns.max_ibgp + ' equal-cluster-length' %}
{% endif %}
  maximum-paths ibgp {{ns.max_ibgp}}
{% endif %}
{% if 'route_download_filter' in af_val %}
  table-map {{af_val['route_download_filter']}}
{% endif %}
{# -------bgp evpn - start --------------------------- #}
{% if af == 'l2vpn_evpn' %}
{% include "bgpd.conf.db.addr_family.evpn.j2" %}
{% endif %}
{# -------bgp evpn - end --------------------------- #}
{% endif %}
{% endfor %}
{% endif %}
 exit-address-family
{% endfor %}
{% endif %}
{# -------address-family end --------------------------- #}
