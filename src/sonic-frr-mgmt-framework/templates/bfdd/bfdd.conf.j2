!
{% block banner %}
! =========== Managed by sonic-cfggen DO NOT edit manually! ====================
! generated by templates/frr/bfd.conf.j2 with config DB data
! file: bfd.conf
!
{% endblock banner %}
!
{% block bfd_shop_peers %}
{% if BFD_PEER_SINGLE_HOP %}
bfd
{% for (peer_key, peer_info) in BFD_PEER_SINGLE_HOP.items() %}
{% set peer_args = '' %}
{% if peer_key[0] != 'null' %}
{% set peer_args = peer_args~' '~'peer'~' '~peer_key[0] %}
{% endif %}
{% if peer_key[1] != 'null' %}
{% set peer_args = peer_args~' '~'interface'~' '~peer_key[1] %}
{% endif %}
{% if peer_key[2] != 'null' %}
{% set peer_args = peer_args~' '~'vrf'~' '~peer_key[2] %}
{% endif %}
{% if peer_key[3] != 'null' %}
{% set peer_args = peer_args~' '~'local-address'~' '~peer_key[3] %}
{% endif %}
{{ peer_args }}
{# set the bfd peer timers #}
{% if  (peer_info['desired-minimum-tx-interval'] is defined and peer_info['desired-minimum-tx-interval'] | int != 300) %}
  transmit-interval {{ peer_info['desired-minimum-tx-interval'] }}
{% endif %}
{% if  (peer_info['required-minimum-receive'] is defined and peer_info['required-minimum-receive'] | int != 300) %}
  receive-interval {{ peer_info['required-minimum-receive'] }}
{% endif %}
{% if  (peer_info['detection-multiplier'] is defined and peer_info['detection-multiplier'] | int != 3) %}
  detect-multiplier {{ peer_info['detection-multiplier'] }}
{% endif %}
{% if  (peer_info['echo-active'] is defined and peer_info['echo-active'] != 'false') %}
  echo-mode
{% endif %}
{% if  (peer_info['desired-minimum-echo-receive'] is defined and peer_info['desired-minimum-echo-receive'] | int != 50) %}
  echo-interval {{ peer_info['desired-minimum-echo-receive'] }}
{% endif %}
{% if  (peer_info['enabled'] is defined and peer_info['enabled'] == 'true') %}
  no shutdown
{% else %}
  shutdown
{% endif %}
  !
{% endfor %}
!
{% endif %}
{% endblock bfd_shop_peers%}
{% block bfd_mhop_peers %}
{% if BFD_PEER_MULTI_HOP %}
bfd
{% for (peer_key, peer_info) in BFD_PEER_MULTI_HOP.items() %}
{% set peer_args = '' %}
{% if peer_key[0] != 'null' %}
{% set peer_args = peer_args~' '~'peer'~' '~peer_key[0] %}
{% endif %}
{% if peer_key[1] != 'null' %}
{% set peer_args = peer_args~' '~'interface'~' '~peer_key[1] %}
{% endif %}
{% if peer_key[2] != 'null' %}
{% set peer_args = peer_args~' '~'vrf'~' '~peer_key[2] %}
{% endif %}
{% if peer_key[3] != 'null' %}
{% set peer_args = peer_args~' '~'local-address'~' '~peer_key[3] %}
{% endif %}
{% set peer_args = peer_args~' '~'multihop' %}
{{ peer_args }}
{# set the bfd peer timers #}
{% if  (peer_info['desired-minimum-tx-interval'] is defined and peer_info['desired-minimum-tx-interval'] | int != 300) %}
  transmit-interval {{ peer_info['desired-minimum-tx-interval'] }}
{% endif %}
{% if  (peer_info['required-minimum-receive'] is defined and peer_info['required-minimum-receive'] | int != 300) %}
  receive-interval {{ peer_info['required-minimum-receive'] }}
{% endif %}
{% if  (peer_info['detection-multiplier'] is defined and peer_info['detection-multiplier'] | int != 3) %}
  detect-multiplier {{ peer_info['detection-multiplier'] }}
{% endif %}
{% if  (peer_info['enabled'] is defined and peer_info['enabled'] == 'true') %}
  no shutdown
{% else %}
  shutdown
{% endif %}
  !
{% endfor %}
!
{% endif %}
{% endblock bfd_mhop_peers%}
