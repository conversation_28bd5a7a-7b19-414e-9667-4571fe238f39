{# -------------------------------------------------------------------------- #}
{# ---------------ISIS_GLOBAL Begin------------------------------------------ #}
{% if ISIS_GLOBAL is defined and ISIS_GLOBAL|length > 0 %}
{% for (isis_global_key, isis_global_data) in ISIS_GLOBAL.items() %}
!
{% set area_tag = isis_global_key[0] %}
router isis {{ area_tag }}
{% if 'level_capability' in isis_global_data %}
{% if isis_intf_data['level_capability'] == 'level-2' %}
 is-type level-2-only
{% else %}
 is-type {{ isis_global_data['level_capability'] }}
{% endif %}
{% endif %}
{% if 'dynamic_hostname' in isis_global_data %}
{% if isis_global_data['dynamic_hostname'] == 'false' %}
 no hostname dynamic
{% endif %}
{% endif %}
{% if 'log_adjacency_changes' in isis_global_data %}
{% if isis_global_data['log_adjacency_changes'] == 'true' %}
 log-adjacency-changes
{% endif %}
{% endif %}
{% if 'lsp_mtu_size' in isis_global_data %}
 lsp-mtu {{ isis_global_data['lsp_mtu_size'] }}
{% endif %}
{% if 'metric_style' in isis_global_data %}
 metric-style {{ isis_global_data['metric_style'] }}
{% endif %}
{% if 'spf_prefix_priority_critical_acl' in isis_global_data %}
 spf prefix-priority critical {{ isis_global_data['spf_prefix_priority_critical_acl'] }}
{% endif %}
{% if 'spf_prefix_priority_high_acl' in isis_global_data %}
 spf prefix-priority high {{ isis_global_data['spf_prefix_priority_high_acl'] }}
{% endif %}
{% if 'spf_prefix_priority_medium_acl' in isis_global_data %}
 spf prefix-priority medium {{ isis_global_data['spf_prefix_priority_medium_acl'] }}
{% endif %}
exit
{% endfor %}
{% endif %}
{# ---------------ISIS_GLOBAL End-------------------------------------------- #}
{# -------------------------------------------------------------------------- #}
{# ---------------ISIS_GLOBAL_TOPOLOGY Begin--------------------------------- #}
{% if ISIS_GLOBAL_TOPOLOGY is defined and ISIS_GLOBAL|length > 0 %}
{% for (isis_global_topo_key, isis_global_topo_data) in ISIS_GLOBAL_TOPOLOGY.items() %}
!
{% set area_tag = isis_global_topo_key[0] %}
{% set proto    = isis_global_topo_key[1] %}
{% set topo     = isis_global_topo_key[2] %}
router isis {{ area_tag }}
{% if 'enabled' in isis_global_topo_data and 'overload_bit' in isis_global_topo_data %}
{% if isis_global_topo_data['enabled'] == 'true' %}
{% if isis_intf_data['overload_bit'] == 'true' %}
 topology {{ proto }}-{{ topo }} overload
{% else %}
 topology {{ proto }}-{{ topo }}
{% endif %}
{% endif %}
{% endif %}
exit
{% endfor %}
{% endif %}
{# ---------------ISIS_GLOBAL_TOPOLOGY End----------------------------------- #}
{# -------------------------------------------------------------------------- #}
{# ---------------ISIS_LEVEL Begin------------------------------------------- #}
{% if ISIS_LEVEL is defined and ISIS_LEVEL|length > 0 %}
{% for (isis_level_key, isis_level_data) in ISIS_LEVEL.items() %}
!
{% set area_tag = isis_level_key[0] %}
{% set level    = isis_level_key[1] %}
router isis {{ area_tag }}
{% if 'lsp_generation_interval' in isis_level_data %}
 lsp-gen-interval {{ level }} {{ isis_level_data['lsp_generation_interval'] }}
{% endif %}
{% if 'lsp_refresh_interval' in isis_level_data %}
 lsp-refresh-interval {{ level }} {{ isis_level_data['lsp_refresh_interval'] }}
{% endif %}
{% if 'lsp_lifetime_interval' in isis_level_data %}
 max-lsp-lifetime {{ level }} {{ isis_level_data['lsp_lifetime_interval'] }}
{% endif %}
{% if 'spf_minimum_interval' in isis_level_data %}
 spf-interval {{ level }} {{ isis_level_data['spf_minimum_interval'] }}
{% endif %}
exit
{% endfor %}
{% endif %}
{# ---------------ISIS_LEVEL End--------------------------------------------- #}