{# -------------------------------------------------------------------------- #}
{# ---------------ISIS_INTERFACE Begin--------------------------------------- #}
{% if ISIS_INTERFACE is defined and ISIS_INTERFACE|length > 0 %}
{% for (intfkey, isis_intf_data) in ISIS_INTERFACE.items() %}
!
{% set area_tag = intfkey[0] %}
{% set intfname = intfkey[1] %}
interface {{ intfname }}
{% if 'ipv4_routing' in isis_intf_data %}
{% if isis_intf_data['ipv4_routing'] == 'true' %}
 ip router isis {{ area_tag }}
{% endif %}
{% endif %}
{% if 'ipv6_routing' in isis_intf_data %}
{% if isis_intf_data['ipv6_routing'] == 'true' %}
 ipv6 router isis {{ area_tag }}
{% endif %}
{% endif %}
{% if 'passive' in isis_intf_data %}
{% if isis_intf_data['passive'] == 'true' %}
 isis passive
{% endif %}
{% endif %}
{% if 'hello_padding' in isis_intf_data %}
{% if isis_intf_data['hello_padding'] == 'true' %}
{% else %}
 no isis hello padding
{% endif %}
{% endif %}
{% if 'network_type' in isis_intf_data %}
{% if isis_intf_data['network_type'] == 'POINT_TO_POINT_NETWORK' %}
 isis network point-to-point
{% endif %}
{% endif %}
{% if 'disable_three_way_handshake' in isis_intf_data %}
{% if isis_intf_data['disable_three_way_handshake'] == 'true' %}
 no isis three-way-handshake
{% endif %}
{% endif %}
{% if 'password_type' in isis_intf_data and 'password' in isis_intf_data %}
 isis password {{ isis_intf_data['password_type'] }} {{ isis_intf_data['password'] }}
{% endif %}
{% if 'level_circuit_type' in isis_intf_data %}
{% if isis_intf_data['level_circuit_type'] == 'level-2' %}
 isis circuit-type level-2-only
{% else %}
 isis circuit-type {{ isis_intf_data['level_circuit_type'] }}
{% endif %}
{% endif %}
exit
{% endfor %}
{% endif %}
{# ---------------ISIS_INTERFACE End----------------------------------------- #}
{# -------------------------------------------------------------------------- #}
{# ---------------ISIS_INTERFACE_LEVEL Begin--------------------------------- #}
{% if ISIS_INTERFACE_LEVEL is defined and ISIS_INTERFACE_LEVEL|length > 0 %}
{% for (isis_intf_level_key, isis_intf_level_data) in ISIS_INTERFACE_LEVEL.items() %}
!
{% set area_tag = isis_intf_level_key[0] %}
{% set intfname = isis_intf_level_key[1] %}
{% set level = isis_intf_level_key[2] %}
interface {{ intfname }}
{% if 'csnp_interval' in isis_intf_level_data %}
 isis csnp-interval {{ level }} {{ isis_intf_level_data['csnp_interval'] }}
{% endif %}
{% if 'psnp_interval' in isis_intf_level_data %}
 isis psnp-interval {{ level }} {{ isis_intf_level_data['psnp_interval'] }}
{% endif %}
{% if 'hello_interval' in isis_intf_level_data %}
 isis hello-interval {{ level }} {{ isis_intf_level_data['hello_interval'] }}
{% endif %}
{% if 'hello_multiplier' in isis_intf_level_data %}
 isis hello-multiplier {{ level }} {{ isis_intf_level_data['hello_multiplier'] }}
{% endif %}
{% if 'priority' in isis_intf_level_data %}
 isis priority {{ isis_intf_level_data['priority'] }} {{ level }}
{% endif %}
{% if 'metric' in isis_intf_level_data %}
 isis metric {{ level }} {{ isis_intf_level_data['metric'] }}
{% endif %}
exit
{% endfor %}
{% endif %}
{# ---------------ISIS_INTERFACE_LEVEL End----------------------------------- #}