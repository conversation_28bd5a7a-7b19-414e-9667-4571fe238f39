!
{% block banner %}
! =========== Managed by sonic-cfggen DO NOT edit manually! ====================
! generated by templates/frr.conf.j2 with config DB data
! file: frr.conf
!
{% endblock banner %}
!
{% include "common/daemons.common.conf.j2" %}
!
agentx
!
！Add fpm address for zebra
fpm address 127.0.0.1
！
{% include "zebra/zebra.interfaces.conf.j2" %}
!
{% if MGMT_VRF_CONFIG %}
{% if MGMT_VRF_CONFIG['vrf_global']['mgmtVrfEnabled'] == 'false' %}
{% include "staticd.db.default_route.conf.j2" %}
{% endif %}
{% else %}
{% include "staticd.db.default_route.conf.j2" %}
{% endif %}
!
{% include "staticd.db.conf.j2" %}
!
{% include "bgpd.conf.db.j2" %}
!
{% include "isisd.conf.j2" %}
!
{% include "ospfd.conf.j2" %}
!
{% include "bfdd.conf.j2" %}
!
