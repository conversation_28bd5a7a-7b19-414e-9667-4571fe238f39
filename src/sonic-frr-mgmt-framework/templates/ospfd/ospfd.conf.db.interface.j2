{# ------------------------------------------------------------------------------------ #}
{# ------------------------------------------------------------------------------------ #}
{% if OSPFV2_INTERFACE is defined and OSPFV2_INTERFACE|length > 0 %}
{% for (intfkey, ospf_intf_instance) in OSPFV2_INTERFACE.items() %}
!
{% set intfname = intfkey[0] %}
{% set intfaddr = intfkey[1] %}
{% if intfaddr == "0.0.0.0" %}
{% set intfaddr = "" %}
{% endif %}
interface {{ intfname }}
{% if 'area-id' in ospf_intf_instance %}
  ip ospf area {{ ospf_intf_instance['area-id'] }} {{ intfaddr }}
{% endif %}
{% if 'network-type' in ospf_intf_instance %}
{% if ospf_intf_instance['network-type'] == 'BROADCAST_NETWORK' %}
  ip ospf network broadcast {{ intfaddr }}
{% endif %}
{% if ospf_intf_instance['network-type'] == 'POINT_TO_POINT_NETWORK' %}
  ip ospf network point-to-point {{ intfaddr }}
{% endif %}
{% endif %}
{% if 'metric' in ospf_intf_instance %}
  ip ospf cost {{ ospf_intf_instance['metric'] }} {{ intfaddr }}
{% endif %}
{% if 'priority' in ospf_intf_instance %}
  ip ospf priority {{ ospf_intf_instance['priority'] }} {{ intfaddr }}
{% endif %}
{% if 'mtu-ignore' in ospf_intf_instance %}
{% if ospf_intf_instance['mtu-ignore'] == 'true' %}
  ip ospf mtu-ignore {{ intfaddr }}
{% endif %}
{% endif %}
{% if 'bfd-enable' in ospf_intf_instance %}
{% if ospf_intf_instance['bfd-enable'] == 'true' %}
  ip ospf bfd
{% endif %}
{% endif %}
{% if 'authentication-type' in ospf_intf_instance %}
{% if ospf_intf_instance['authentication-type'] == 'MD5HMAC' %}
  ip ospf authentication message-digest {{ intfaddr }}
{% endif %}
{% if ospf_intf_instance['authentication-type'] == 'NONE' %}
  ip ospf authentication null {{ intfaddr }}
{% endif %}
{% if ospf_intf_instance['authentication-type'] == 'TEXT' %}
  ip ospf authentication {{ intfaddr }}
{% endif %}
{% endif %}
{% if 'authentication-key' in ospf_intf_instance %}
  ip ospf authentication-key {{ ospf_intf_instance['authentication-key'] }} {{ intfaddr }}
{% endif %}
{% if 'authentication-key-id' in ospf_intf_instance and 'authentication-md5-key' in ospf_intf_instance %}
  ip ospf message-digest-key {{ ospf_intf_instance['authentication-key-id'] }} md5 {{ ospf_intf_instance['authentication-md5-key'] }} {{ intfaddr }}
{% endif %}
{% if 'dead-interval' in ospf_intf_instance %}
  ip ospf dead-interval {{ ospf_intf_instance['dead-interval'] }} {{ intfaddr }}
{% endif %}
{% if 'dead-interval-minimal' in ospf_intf_instance %}
{% if 'hello-multiplier' in ospf_intf_instance %}
  ip ospf dead-interval minimal hello-multiplier {{ ospf_intf_instance['hello-multiplier'] }} {{ intfaddr }}
{% else %}
  ip ospf dead-interval minimal {{ intfaddr }}
{% endif %}
{% endif %}
{% if 'hello-interval' in ospf_intf_instance %}
  ip ospf hello-interval {{ ospf_intf_instance['hello-interval'] }} {{ intfaddr }}
{% endif %}
{% if 'retransmission-interval' in ospf_intf_instance %}
  ip ospf retransmit-interval {{ ospf_intf_instance['retransmission-interval'] }} {{ intfaddr }}
{% endif %}
{% if 'transmit-delay' in ospf_intf_instance %}
  ip ospf transmit-delay {{ ospf_intf_instance['transmit-delay'] }} {{ intfaddr }}
{% endif %}
{% endfor %}
{% endif %}
