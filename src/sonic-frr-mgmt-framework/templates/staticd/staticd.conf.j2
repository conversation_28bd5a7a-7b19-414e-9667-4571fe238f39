
{% block banner %}
! =========== Managed by sonic-cfggen DO NOT edit manually! ====================
! generated by templates/frr/staticd.conf.j2 using config DB data
! file: staticd.conf
!
{% endblock banner %}
!
{% with agentx='false' %}
{% include "common/daemons.common.conf.j2" %}
{% endwith %}
!
{% if MGMT_VRF_CONFIG %}
{% if MGMT_VRF_CONFIG['vrf_global']['mgmtVrfEnabled'] == 'false' %}
{% include "staticd.db.default_route.conf.j2" %}
{% endif %}
{% else %}
{% include "staticd.db.default_route.conf.j2" %}
{% endif %}
!
{% include "staticd.db.conf.j2" %}
!
