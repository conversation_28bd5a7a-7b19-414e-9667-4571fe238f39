#!/usr/bin/env python3

"""
Test script to verify VRRP configuration synchronization from Redis to FRR
"""

import sys
import os
import time
import redis
import subprocess
import json
import logging

# Add the frrcfgd directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'frrcfgd'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_redis():
    """Connect to Redis database"""
    try:
        r = redis.Redis(host='127.0.0.1', port=6379, db=4, decode_responses=True)
        r.ping()
        logger.info("Connected to Redis successfully")
        return r
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        return None

def run_vtysh_command(command):
    """Run a vtysh command and return the output"""
    try:
        cmd = f"vtysh -c '{command}'"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        logger.error(f"Failed to run vtysh command '{command}': {e}")
        return -1, "", str(e)

def get_vrrp_config():
    """Get current VRRP configuration from FRR"""
    ret_code, stdout, stderr = run_vtysh_command("show running-config vrrpd")
    if ret_code == 0:
        return stdout
    else:
        logger.error(f"Failed to get VRRP config: {stderr}")
        return None

def test_vrrp_basic_config():
    """Test basic VRRP configuration"""
    logger.info("Testing basic VRRP configuration...")
    
    r = connect_redis()
    if not r:
        return False
    
    # Test data
    test_key = "VRRP|Ethernet0|1"
    test_data = {
        "vrid": "1",
        "version": "3", 
        "priority": "100",
        "adv_interval": "1000",
        "pre_empt": "true",
        "use_v2_checksum": "false",
        "vip": "***********"
    }
    
    try:
        # Clear any existing data
        r.delete(test_key)
        time.sleep(1)
        
        # Set VRRP configuration
        logger.info(f"Setting VRRP config: {test_key} -> {test_data}")
        r.hset(test_key, mapping=test_data)
        
        # Wait for frrcfgd to process the change
        time.sleep(3)
        
        # Check FRR configuration
        config = get_vrrp_config()
        if config:
            logger.info("Current FRR VRRP configuration:")
            logger.info(config)
            
            # Check if VRRP instance was created
            if "vrrp 1" in config:
                logger.info("✓ VRRP instance created successfully")
                return True
            else:
                logger.error("✗ VRRP instance not found in FRR config")
                return False
        else:
            logger.error("✗ Failed to get FRR configuration")
            return False
            
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        return False
    finally:
        # Cleanup
        try:
            r.delete(test_key)
        except:
            pass

def test_vrrp_update_config():
    """Test VRRP configuration updates"""
    logger.info("Testing VRRP configuration updates...")
    
    r = connect_redis()
    if not r:
        return False
    
    test_key = "VRRP|Ethernet0|2"
    
    try:
        # Initial configuration
        initial_data = {
            "vrid": "2",
            "priority": "50",
            "vip": "***********"
        }
        
        logger.info(f"Setting initial VRRP config: {test_key} -> {initial_data}")
        r.hset(test_key, mapping=initial_data)
        time.sleep(2)
        
        # Update configuration
        update_data = {
            "priority": "150",
            "pre_empt": "true",
            "adv_interval": "2000"
        }
        
        logger.info(f"Updating VRRP config: {test_key} -> {update_data}")
        r.hset(test_key, mapping=update_data)
        time.sleep(2)
        
        # Check final configuration
        config = get_vrrp_config()
        if config:
            logger.info("Updated FRR VRRP configuration:")
            logger.info(config)
            
            if "vrrp 2" in config and "priority 150" in config:
                logger.info("✓ VRRP configuration updated successfully")
                return True
            else:
                logger.error("✗ VRRP configuration update not reflected in FRR")
                return False
        else:
            logger.error("✗ Failed to get updated FRR configuration")
            return False
            
    except Exception as e:
        logger.error(f"Update test failed with exception: {e}")
        return False
    finally:
        # Cleanup
        try:
            r.delete(test_key)
        except:
            pass

def test_vrrp_delete_config():
    """Test VRRP configuration deletion"""
    logger.info("Testing VRRP configuration deletion...")
    
    r = connect_redis()
    if not r:
        return False
    
    test_key = "VRRP|Ethernet0|3"
    
    try:
        # Create configuration
        test_data = {
            "vrid": "3",
            "priority": "200",
            "vip": "***********"
        }
        
        logger.info(f"Creating VRRP config for deletion test: {test_key} -> {test_data}")
        r.hset(test_key, mapping=test_data)
        time.sleep(2)
        
        # Verify creation
        config = get_vrrp_config()
        if config and "vrrp 3" not in config:
            logger.warning("VRRP instance not created, skipping deletion test")
            return False
        
        # Delete configuration
        logger.info(f"Deleting VRRP config: {test_key}")
        r.delete(test_key)
        time.sleep(2)
        
        # Check deletion
        config = get_vrrp_config()
        if config:
            logger.info("FRR VRRP configuration after deletion:")
            logger.info(config)
            
            if "vrrp 3" not in config:
                logger.info("✓ VRRP configuration deleted successfully")
                return True
            else:
                logger.error("✗ VRRP configuration still exists after deletion")
                return False
        else:
            logger.info("✓ VRRP configuration deleted (no config found)")
            return True
            
    except Exception as e:
        logger.error(f"Deletion test failed with exception: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Starting VRRP configuration synchronization tests...")
    
    # Check if frrcfgd is running
    try:
        result = subprocess.run("pgrep -f frrcfgd", shell=True, capture_output=True)
        if result.returncode != 0:
            logger.warning("frrcfgd process not found. Please start frrcfgd before running tests.")
            logger.info("You can start it with: python3 frrcfgd/frrcfgd.py")
    except:
        pass
    
    tests = [
        ("Basic VRRP Configuration", test_vrrp_basic_config),
        ("VRRP Configuration Updates", test_vrrp_update_config),
        ("VRRP Configuration Deletion", test_vrrp_delete_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            if test_func():
                logger.info(f"✓ {test_name} PASSED")
                passed += 1
            else:
                logger.error(f"✗ {test_name} FAILED")
        except Exception as e:
            logger.error(f"✗ {test_name} FAILED with exception: {e}")
        
        time.sleep(1)
    
    logger.info(f"\n{'='*60}")
    logger.info(f"Test Results: {passed}/{total} tests passed")
    logger.info(f"{'='*60}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
